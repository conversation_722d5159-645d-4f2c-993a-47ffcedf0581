package com.biz.eisp.sci.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.net.ssl.*;
import java.security.cert.X509Certificate;

/**
 * SSL配置类，用于跳过SSL证书验证
 * 注意：此配置仅用于开发和测试环境，生产环境请使用正确的SSL证书
 */
@Configuration
public class SSLConfig {

    private static final Logger logger = LoggerFactory.getLogger(SSLConfig.class);

    @PostConstruct
    public void disableSSLVerification() {
        try {
            // 创建信任所有证书的TrustManager
            TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                        // 不做任何检查
                    }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                        // 不做任何检查
                    }
                }
            };

            // 安装信任所有证书的TrustManager
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            // 创建跳过主机名验证的HostnameVerifier
            HostnameVerifier allHostsValid = new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
            
            logger.info("SSL证书验证已全局跳过");
        } catch (Exception e) {
            logger.error("SSL配置失败", e);
        }
    }
}
