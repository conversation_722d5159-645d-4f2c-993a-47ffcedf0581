package com.biz.eisp.sci.tebo;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.security.MessageDigest;
import java.util.Collections;
import java.util.List;

public class TeboShopClientUtil {

    private static final String API_URL = "https://www.tntab.cn/prod-api/tnyShop/shopInfo/list4Gg";
    private static final String SECRET = "tebo_secret_key";
    private static final RestTemplate restTemplate = new RestTemplate();
    private static final Logger logger = LoggerFactory.getLogger(TeboShopClientUtil.class);

    public static List<TeboShopResp> queryShops(TeboShopReq req) {
        if (req == null) {
            logger.error("请求参数不能为空");
            return Collections.emptyList();
        }
        if (req.getPageNum() == null){
            req.setPageNum(1);
        }
        if (req.getPageSize() == null){
            req.setPageSize(10);
        }
        String base = String.format("%s|%s|%s|%s|%s",
                req.getId() == null ? "" : req.getId(),
                nullToEmpty(req.getKeyWord()),
                nullToEmpty(req.getProvince()),
                nullToEmpty(req.getCity()),
                SECRET);
        req.setSign(md5(base));

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<TeboShopReq> entity = new HttpEntity<>(req, headers);

        try {
            ResponseEntity<TeboApiResponse<TeboShopListWrapper>> response = restTemplate.exchange(
                    API_URL,
                    HttpMethod.POST,
                    entity,
                    new ParameterizedTypeReference<TeboApiResponse<TeboShopListWrapper>>() {}
            );

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                TeboApiResponse<TeboShopListWrapper> result = response.getBody();
                if ("200".equals(result.getCode()) && result.getData() != null) {
                    return result.getData().getList();
                }
            }
        } catch (Exception e) {
            logger.error("请求异常", e);
        }

        return Collections.emptyList();
    }

    private static String nullToEmpty(String val) {
        return val == null ? "" : val;
    }

    private static String md5(String text) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] bytes = md.digest(text.getBytes("UTF-8"));
            StringBuilder sb = new StringBuilder();
            for (byte b : bytes) {
                sb.append(String.format("%02x", b & 0xff));
            }
            return sb.toString();
        } catch (Exception e) {
            throw new RuntimeException("MD5计算失败", e);
        }
    }

    public static void main(String[] args) {
        TeboShopReq req = new TeboShopReq();
        req.setKeyWord("15757107362");
        req.setPageNum(1);
        req.setPageSize(10);

        List<TeboShopResp> shops = queryShops(req);
        if (shops.isEmpty()) {
            logger.info("未查询到门店信息");
        } else {
            for (TeboShopResp shop : shops) {
                logger.info("门店信息: {}", shop);
            }
        }
    }
}
