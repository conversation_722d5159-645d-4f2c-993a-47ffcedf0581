package com.biz.eisp.sci.api.mdm.controller;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.biz.eisp.api.util.OwnBeanUtils;
import com.biz.eisp.base.common.exception.BusinessException;

import com.biz.eisp.base.common.util.StringUtil;
import com.biz.eisp.mdm.dict.entity.TmDictDataEntity;
import com.biz.eisp.sci.api.mdm.vo.*;
import com.biz.eisp.sci.pi.pimpl.vo.UserAccountVo;
import com.biz.eisp.sci.tebo.TeboShopClientUtil;
import com.biz.eisp.sci.tebo.TeboShopReq;
import com.biz.eisp.sci.tebo.TeboShopResp;
import com.biz.eisp.sci.util.SfaGlobals;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.biz.eisp.base.common.constant.Globals;
import com.biz.eisp.base.common.util.CollectionUtil;
import com.biz.eisp.base.core.page.Page;
import com.biz.eisp.base.core.web.BaseController;
import com.biz.eisp.mdm.customer.service.TmCustomerService;
import com.biz.eisp.mdm.customer.vo.QueryTmCustomerVo;
import com.biz.eisp.mdm.customer.vo.TmCustomerVo;
import com.biz.eisp.mdm.dict.service.TmDictDataService;
import com.biz.eisp.mdm.dict.util.DictUtil;
import com.biz.eisp.mdm.dict.vo.TmDictDataVo;
import com.biz.eisp.mdm.position.entity.TmPositionEntity;
import com.biz.eisp.mdm.position.service.TmPositionService;
import com.biz.eisp.mdm.position.vo.QueryTmpositionVo;
import com.biz.eisp.mdm.position.vo.TmPositionVo;
import com.biz.eisp.mdm.terminal.entity.TmTerminalEntity;
import com.biz.eisp.mdm.terminal.vo.TmTermCustPostVo;
import com.biz.eisp.sci.api.mdm.service.TmTerminalForSciService;
import com.biz.eisp.sci.pi.pimpl.vo.PhoneConstantUtil;
import com.biz.eisp.sci.pi.util.json.Head;
import com.biz.eisp.sci.pi.util.json.ResponseBean;
import com.biz.eisp.sci.travelleave.vo.LeaveTypeVo;

@Controller
@RequestMapping("/tmTerminalForSfaController")
public class TmTerminalForSfaController extends BaseController{

	private static final Logger log = LoggerFactory.getLogger(TmTerminalForSfaController.class);
	@Autowired
	private TmTerminalForSciService tmTerminalForSciService;
	@Autowired
	private TmDictDataService tmDictDataService;
	@Autowired
	private TmCustomerService tmCustomerService;
	@Autowired
	private TmPositionService tmPositionService;


	/**
	 * 手机端读取终端类型
	 * @return
	 * @url : tmTerminalForSfaController.do?getTerminalType
	 */
	@RequestMapping(params = "getTerminalType")
	@ResponseBody
	public ResponseBean getTerminalType(UserAccountVo use){
		ResponseBean json = new ResponseBean();
		Head head = new Head();
		head.setCode(Globals.RETURN_FAIL);
		String title = "获取终端类型";
		head.setMessage( title + "失败");
		try {
			List<TmDictDataVo> tmDictDataEntities = DictUtil.allDictData.get(PhoneConstantUtil.termianlType);
			json.setBusinessObject(OwnBeanUtils.encapsulateDataAjaxByMap(tmDictDataEntities,"dictCode","dictValue"));
			head.setCode(Globals.RETURN_SUCCESS);
			head.setMessage(title + "成功");
		}catch (Exception e){
			e.printStackTrace();
		}
		json.setHead(head);
		return json;
	}

	/**
	 * 手机端读取终端主营业务
	 * @return
	 * @url : tmTerminalForSfaController.do?getTermianlSale
	 */
	@RequestMapping(params = "getTermianlSale")
	@ResponseBody
	public ResponseBean getTermianlSale(UserAccountVo use){
		ResponseBean json = new ResponseBean();
		Head head = new Head();
		head.setCode(Globals.RETURN_FAIL);
		String title = "获取终端主营业务";
		head.setMessage( title + "失败");
		try {
			List<TmDictDataVo> tmDictDataEntities = DictUtil.allDictData.get(PhoneConstantUtil.termianlSale);
			json.setBusinessObject(OwnBeanUtils.encapsulateDataAjaxByMap(tmDictDataEntities,"dictCode","dictValue"));
			head.setCode(Globals.RETURN_SUCCESS);
			head.setMessage(title + "成功");
		}catch (Exception e){
			e.printStackTrace();
		}
		json.setHead(head);
		return json;
	}

	/**
	 * 手机端读取终端进货渠道
	 * @return
	 * @url : tmTerminalForSfaController.do?getTermianlBuyChannel
	 */
	@RequestMapping(params = "getTermianlBuyChannel")
	@ResponseBody
	public ResponseBean getTermianlBuyChannel(UserAccountVo use){
		ResponseBean json = new ResponseBean();
		Head head = new Head();
		head.setCode(Globals.RETURN_FAIL);
		String title = "获取终端进货渠道";
		head.setMessage( title + "失败");
		try {
			List<TmDictDataVo> tmDictDataEntities = DictUtil.allDictData.get(PhoneConstantUtil.termianlBuyChannel);
			json.setBusinessObject(OwnBeanUtils.encapsulateDataAjaxByMap(tmDictDataEntities,"dictCode","dictValue"));
			head.setCode(Globals.RETURN_SUCCESS);
			head.setMessage(title + "成功");
		}catch (Exception e){
			e.printStackTrace();
		}
		json.setHead(head);
		return json;
	}

	/**
	 * 手机端读取终端主要卖的竞品电池品牌
	 * @return
	 * @url : tmTerminalForSfaController.do?getTermianlSaleBrand
	 */
	@RequestMapping(params = "getTermianlSaleBrand")
	@ResponseBody
	public ResponseBean getTermianlSaleBrand(UserAccountVo use){
		ResponseBean json = new ResponseBean();
		Head head = new Head();
		head.setCode(Globals.RETURN_FAIL);
		String title = "获取终端主卖竞品品牌";
		head.setMessage( title + "失败");
		try {
			List<TmDictDataVo> tmDictDataEntities = DictUtil.allDictData.get(PhoneConstantUtil.termianlSaleBrand);
			json.setBusinessObject(OwnBeanUtils.encapsulateDataAjaxByMap(tmDictDataEntities,"dictCode","dictValue"));
			head.setCode(Globals.RETURN_SUCCESS);
			head.setMessage(title + "成功");
		}catch (Exception e){
			e.printStackTrace();
		}
		json.setHead(head);
		return json;
	}

	/**
	 * 手机端读取终端所属大区
	 * @return
	 * @url : tmTerminalForSfaController.do?getTermianlSsdq
	 */
	@RequestMapping(params = "getTermianlSsdq")
	@ResponseBody
	public ResponseBean getTermianlSsdq(TmTerminalExtendVo use){
		ResponseBean json = new ResponseBean();
		Head head = new Head();
		head.setCode(Globals.RETURN_FAIL);
		String title = "获取终端所属大区";
		head.setMessage( title + "失败");
		List<DicVo> list = new ArrayList<DicVo>();
		try {
			String sql ="select tt.dqid,tt.dqname from TGRELATION tt where tt.tgid = ? group by tt.dqid,tt.dqname ";
			List<TgRelationVo> Tlist = tmTerminalForSciService.findBySql(TgRelationVo.class,sql,use.getUserName());
			if(Tlist != null && Tlist.size() > 0){
				DicVo dic = null;
				for(TgRelationVo bean : Tlist){
                    if(bean.getDqid()!= null && !"".equals(bean.getDqid()) && bean.getDqname()!= null && !"".equals(bean.getDqname())){
						dic = new DicVo();
						dic.setDictCode(bean.getDqid());
                        dic.setDictValue(bean.getDqname());
                        list.add(dic);
					}
				}
			}
			json.setBusinessObject(OwnBeanUtils.encapsulateDataAjaxByMap(list,"dictCode","dictValue"));
			head.setCode(Globals.RETURN_SUCCESS);
			head.setMessage(title + "成功");
		}catch (Exception e){
			e.printStackTrace();
		}
		json.setHead(head);
		return json;
	}

	/**
	 * 手机端读取终端所属区域经理
	 * @return
	 * @url : tmTerminalForSfaController.do?getTermianlSsManager
	 */
	@RequestMapping(params = "getTermianlSsManager")
	@ResponseBody
	public ResponseBean getTermianlSsManager(TmTerminalExtendVo use){
		ResponseBean json = new ResponseBean();
		Head head = new Head();
		head.setCode(Globals.RETURN_FAIL);
		String title = "获取终端所属区域经理";
		head.setMessage( title + "失败");
		List<DicVo> list = new ArrayList<DicVo>();
		try {
			String sql ="select tt.maid,tt.maname from TGRELATION tt where tt.tgid = ? group by  tt.maid,tt.maname ";
			List<TgRelationVo> Tlist = tmTerminalForSciService.findBySql(TgRelationVo.class,sql,use.getUserName());

			if(Tlist != null && Tlist.size() > 0){

				DicVo dic = null;
				for(TgRelationVo bean : Tlist){
					if(bean.getMaid()!= null && !"".equals(bean.getMaid()) && bean.getManame()!= null && !"".equals(bean.getManame())){
						dic = new DicVo();
						dic.setDictCode(bean.getMaid());
						dic.setDictValue(bean.getManame());
						list.add(dic);
					}
				}
			}
			json.setBusinessObject(OwnBeanUtils.encapsulateDataAjaxByMap(list,"dictCode","dictValue"));
			head.setCode(Globals.RETURN_SUCCESS);
			head.setMessage(title + "成功");
		}catch (Exception e){
			e.printStackTrace();
		}
		json.setHead(head);
		return json;
	}

	/**
	 * 获取所属经销商    2020-02-18  白池标
	 * 		 终端vo扩展对象
	 * @url : tmTerminalForSfaController.do?findCustomerByUserid
	 */
//	@RequestMapping(params="findCustomerByUserid")
//	@ResponseBody
//	public ResponseBean findCustomerByUserid(TmCustomerSfaVo qtcVo){
//		ResponseBean bean = new ResponseBean();
//		Page page = setPage(qtcVo.getPage(),qtcVo.getRows());
//		Head head = new Head();
//		try {
//			List<TmCustomerVo> tcvoList = tmTerminalForSciService.findTmCustTomerByAllPossible(qtcVo,page);
//			head.setCode(Globals.RETURN_SUCCESS);
//			head.setMessage("获取所属经销商成功");
//			bean.setBusinessObject(tcvoList);
//		} catch (Exception e) {
//			head.setCode(Globals.RETURN_FAIL);
//			head.setMessage("获取所属经销商失败,"+e.getMessage());
//			e.printStackTrace();
//		}
//		bean.setHead(head);
//		return bean;
//	}


	/**
	 * 手机端读取终端所属经销商
	 * @return
	 * @url : tmTerminalForSfaController.do?findCustomerByUserid
	 */
	@RequestMapping(params = "findCustomerByUserid")
	@ResponseBody
	public ResponseBean findCustomerByUserid(TmCustomerSfaVo use){
		ResponseBean json = new ResponseBean();
		Head head = new Head();
		head.setCode(Globals.RETURN_FAIL);
		String title = "获取终端所属经销商";
		head.setMessage( title + "失败");
		List<TmCustomerSfaVo> list = new ArrayList<TmCustomerSfaVo>();
		try {
			String sql ="select tt.cuid,tt.cuname from TGRELATION tt where tt.tgid = ? group by  tt.cuid,tt.cuname ";
			List<TgRelationVo> Tlist = tmTerminalForSciService.findBySql(TgRelationVo.class,sql,use.getUserName());

			if(Tlist != null && Tlist.size() > 0){

				TmCustomerSfaVo dic = null;
				for(TgRelationVo bean : Tlist){
					if(bean.getCuid()!= null && !"".equals(bean.getCuid()) && bean.getCuname()!= null && !"".equals(bean.getCuname())){
						dic = new TmCustomerSfaVo();
						dic.setCustomerCode(bean.getCuid());
						dic.setCustomerName(bean.getCuname());
						list.add(dic);
					}
				}
				json.setBusinessObject(list);
			} else {//如果不是推广，就按区域经理的身份登记
				List<TmCustomerVo> tcvoList = tmTerminalForSciService.findTmCustTomerByAllPossible(use,page);
				json.setBusinessObject(tcvoList);
			}

			head.setCode(Globals.RETURN_SUCCESS);
			head.setMessage(title + "成功");
		}catch (Exception e){
			e.printStackTrace();
		}
		json.setHead(head);
		return json;
	}

	private static final String SQL_1 ="select count(*) as nums from tncrm.tm_terminal t " +
			" where t.ssjxs is not null and to_char(t.create_date,'yyyy-MM-dd') = to_char(sysdate,'yyyy-MM-dd') " +
			"   and substr(t.ext_char_10,0,6) = ? ";
	private static final String SQL_2 ="select count(*) as nums from tncrm.tm_terminal t " +
			" where t.ssjxs is not null and to_char(t.create_date,'yyyy-MM') = to_char(sysdate,'yyyy-MM') " +
			"   and substr(t.ext_char_10,0,6) = ? ";
	private static final String SQL_3 ="select count(*) as nums from tncrm.tm_terminal t " +
			" where t.ssjxs is not null  " +
			"   and substr(t.ext_char_10,0,6) = ? ";
	private static final String SQL_11 ="select names , nums from ( " +
			"select tgname as names, count(*) as nums from tncrm.tm_terminal t " +
			"left join (select tgid,(dqname||'-'|| tgname) as tgname from tncrm.tgrelation group by tgid,(dqname||'-'|| tgname)) k on k.tgid = substr(t.ext_char_10,0,6) " +
			" where t.ssjxs is not null and to_char(t.create_date,'yyyy-MM-dd') = to_char(sysdate,'yyyy-MM-dd') " +
			" group by tgname order by count(*) desc) where rownum<=1 ";
	private static final String SQL_12 ="select names , nums from ( " +
			"select tgname as names, count(*) as nums from tncrm.tm_terminal t " +
			"left join (select tgid,(dqname||'-'|| tgname) as tgname from tncrm.tgrelation group by tgid,(dqname||'-'|| tgname)) k on k.tgid = substr(t.ext_char_10,0,6) " +
			" where t.ssjxs is not null and to_char(t.create_date,'yyyy-MM') = to_char(sysdate,'yyyy-MM') " +
			" group by tgname order by count(*) desc) where rownum<=1 ";
	private static final String SQL_13 ="select names , nums from ( " +
			"select tgname as names, count(*) as nums from tncrm.tm_terminal t " +
			"left join (select tgid,(dqname||'-'|| tgname) as tgname from tncrm.tgrelation group by tgid,(dqname||'-'|| tgname)) k on k.tgid = substr(t.ext_char_10,0,6) " +
			" where t.ssjxs is not null  " +
			" group by tgname order by count(*) desc) where rownum<=1 ";

	/**
	 * 手机端读取终端新增报表
	 * @return
	 * @url : tmTerminalForSfaController.do?findAddTermReportByUserName
	 */
	@RequestMapping(params = "findAddTermReportByUserName")
	@ResponseBody
	public ResponseBean findAddTermReportByUserName(TmCustomerSfaVo use){
		ResponseBean json = new ResponseBean();
		Head head = new Head();
		head.setCode(Globals.RETURN_FAIL);
		String title = "获取终端登记报表";
		head.setMessage( title + "失败");
		List<AddTermialToTVo> result = new ArrayList<AddTermialToTVo>();
		AddTermialToTVo bean = new AddTermialToTVo();
		try {
			List<ToTVo> Tlist1 = tmTerminalForSciService.findBySql(ToTVo.class,SQL_1,use.getUserName());
			List<ToTVo> Tlist2 = tmTerminalForSciService.findBySql(ToTVo.class,SQL_2,use.getUserName());
			List<ToTVo> Tlist3 = tmTerminalForSciService.findBySql(ToTVo.class,SQL_3,use.getUserName());
			List<ToTVo> Tlist11 = tmTerminalForSciService.findBySql(ToTVo.class,SQL_11);
			List<ToTVo> Tlist12 = tmTerminalForSciService.findBySql(ToTVo.class,SQL_12);
			List<ToTVo> Tlist13 = tmTerminalForSciService.findBySql(ToTVo.class,SQL_13);
			ToTVo vo = null;
			if(Tlist1.size() > 0){
				vo = new ToTVo();
				vo = Tlist1.get(0);
				bean.setNumsDay(vo.getNums()==null?"0":vo.getNums());
			}
			if(Tlist2.size() > 0){
				vo = new ToTVo();
				vo = Tlist2.get(0);
				bean.setNumsMon(vo.getNums()==null?"0":vo.getNums());
			}
			if(Tlist3.size() > 0){
				vo = new ToTVo();
				vo = Tlist3.get(0);
				bean.setNumsYea(vo.getNums()==null?"0":vo.getNums());
			}
			if(Tlist11.size() > 0){
				vo = new ToTVo();
				vo = Tlist11.get(0);
				bean.setNumsDayGj(vo.getNums()==null?"0":vo.getNums());
				bean.setNamesDayGj(vo.getNames()==null?"":vo.getNames());
			}
			if(Tlist12.size() > 0){
				vo = new ToTVo();
				vo = Tlist12.get(0);
				bean.setNumsMonGj(vo.getNums()==null?"0":vo.getNums());
				bean.setNamesMonGj(vo.getNames()==null?"":vo.getNames());
			}
			if(Tlist13.size() > 0){
				vo = new ToTVo();
				vo = Tlist13.get(0);
				bean.setNumsYeaGj(vo.getNums()==null?"0":vo.getNums());
				bean.setNamesYeaGj(vo.getNames()==null?"":vo.getNames());
			}
			bean.setNamesDayGj(bean.getNamesDayGj() == null?"虚位以待":bean.getNamesDayGj());
			bean.setNamesMonGj(bean.getNamesMonGj() == null?"虚位以待":bean.getNamesMonGj());
			bean.setNamesYeaGj(bean.getNamesDayGj() == null?"虚位以待":bean.getNamesYeaGj());
			bean.setNumsDayGj(bean.getNumsDayGj() == null?"0":bean.getNumsDayGj());
			bean.setNumsMonGj(bean.getNumsMonGj() == null?"0":bean.getNumsMonGj());
			bean.setNumsYeaGj(bean.getNumsYeaGj() == null?"0":bean.getNumsYeaGj());

			result.add(bean);
			json.setBusinessObject(result);
			head.setCode(Globals.RETURN_SUCCESS);
			head.setMessage(title + "成功");
		}catch (Exception e){
			e.printStackTrace();
		}
		json.setHead(head);
		return json;
	}
	/**
	 * 保存终端信息终端vo扩展对象   新能源
	 *  @url : tmTerminalForSfaController.do?saveTmTerminalXNY
	 */
	@RequestMapping(params="saveTmTerminalXNY")
	@ResponseBody
	public ResponseBean saveTmTerminalXNY(TmTerminalExtendVo tmTerminalExtendVo){
		ResponseBean bean = new ResponseBean();
		if(tmTerminalExtendVo != null && tmTerminalExtendVo.getCustomerCode() != null){
			tmTerminalExtendVo.setSsjxs(tmTerminalExtendVo.getCustomerCode());
		}
		if(tmTerminalExtendVo != null && tmTerminalExtendVo.getCustomerName() != null){
			tmTerminalExtendVo.setSsjxsval(tmTerminalExtendVo.getCustomerName());
		}

		Head head = new Head();
		try {
			String sql ="select * from tm_terminal tt where tt.ext_char_10 = ?";
			List<TmTerminalEntity> tmList = tmTerminalForSciService.findBySql(TmTerminalEntity.class,sql,tmTerminalExtendVo.getBusinessId());
			if(CollectionUtil.listEmpty(tmList)) {//用于手机离线数据验重(非重复保存)
				tmTerminalForSciService.saveTmTerminalXNY(tmTerminalExtendVo);
			}
			head.setCode(Globals.RETURN_SUCCESS);
			head.setMessage("保存终端信息成功");
		} catch (Exception e) {
			head.setCode(Globals.RETURN_FAIL);
			head.setMessage("保存终端信息失败,"+e.getMessage());
			e.printStackTrace();
		}
		bean.setHead(head);
		return bean;
	}

	/**
	 * 保存终端信息终端vo扩展对象
	 *  @url : tmTerminalForSfaController.do?saveTmTerminal
	 */
	@RequestMapping(params="saveTmTerminal")
	@ResponseBody
	public ResponseBean saveTmTerminal(TmTerminalExtendVo tmTerminalExtendVo){
		ResponseBean bean = new ResponseBean();
		if(tmTerminalExtendVo != null && tmTerminalExtendVo.getCustomerCode() != null){
			tmTerminalExtendVo.setSsjxs(tmTerminalExtendVo.getCustomerCode());
		}
		if(tmTerminalExtendVo != null && tmTerminalExtendVo.getCustomerName() != null){
			tmTerminalExtendVo.setSsjxsval(tmTerminalExtendVo.getCustomerName());
		}

		Head head = new Head();
		head.setCode(Globals.RETURN_FAIL);
		head.setMessage("请在微信搜索“天能广告”小程序，进行操作");
		try {
			String sql ="select * from tm_terminal tt where tt.ext_char_10 = ?";
			List<TmTerminalEntity> tmList = tmTerminalForSciService.findBySql(TmTerminalEntity.class,sql,tmTerminalExtendVo.getBusinessId());
			if(CollectionUtil.listEmpty(tmList)) {//用于手机离线数据验重(非重复保存)
				tmTerminalForSciService.saveTmTerminal(tmTerminalExtendVo);
			}
			head.setCode(Globals.RETURN_SUCCESS);
			head.setMessage("保存门店信息成功");
		} catch (Exception e) {
			head.setCode(Globals.RETURN_FAIL);
			head.setMessage("保存门店信息失败,"+e.getMessage());
			e.printStackTrace();
		}
		bean.setHead(head);
		return bean;
	}
	/**
	 * 获取位置类型
	 * 		 终端vo扩展对象
	 */
	@RequestMapping(params="findLocationType")
	@ResponseBody
	public ResponseBean findLocationType(TmTerminalExtendVo tmTerminalExtendVo){
		ResponseBean bean = new ResponseBean();
		Head head = new Head();
		//数据容器
		List<TmDictDataVo> dictDatas = new ArrayList<TmDictDataVo>();
		try {
			dictDatas.addAll(tmDictDataService.findVoByType(PhoneConstantUtil.locationType));
			if(CollectionUtil.listNotEmptyNotSizeZero(dictDatas)){
				bean.setBusinessObject(dictDatas);
			}
			head.setCode(Globals.RETURN_SUCCESS);
			head.setMessage("获取位置类型信息成功");
		} catch (Exception e) {
			head.setCode(Globals.RETURN_FAIL);
			head.setMessage("获取位置类型信息失败,"+e.getMessage());
			e.printStackTrace();
		}
		bean.setHead(head);
		return bean;
	}

	/**
	 * 获取终端门头类型
	 * 		 终端vo扩展对象
	 */
	@RequestMapping(params="findTerminalAdsTp")
	@ResponseBody
	public ResponseBean findTerminalAdsTp(TmTerminalExtendVo tmTerminalExtendVo){
		ResponseBean bean = new ResponseBean();
		Head head = new Head();
		//数据容器
		List<TmDictDataVo> dictDatas = new ArrayList<TmDictDataVo>();
		try {
			dictDatas.addAll(tmDictDataService.findVoByType(PhoneConstantUtil.termianlAds));
			if(CollectionUtil.listNotEmptyNotSizeZero(dictDatas)){
				bean.setBusinessObject(dictDatas);
			}
			head.setCode(Globals.RETURN_SUCCESS);
			head.setMessage("获取终端门头类型信息成功");
		} catch (Exception e) {
			head.setCode(Globals.RETURN_FAIL);
			head.setMessage("获取终端门头类型信息失败,"+e.getMessage());
			e.printStackTrace();
		}
		bean.setHead(head);
		return bean;
	}

	/**
	 * 获取主销型号
	 * 		 终端vo扩展对象
	 */
	@RequestMapping(params="findTerminalBTModel")
	@ResponseBody
	public ResponseBean findTerminalBTModel(TmTerminalExtendVo tmTerminalExtendVo){
		ResponseBean bean = new ResponseBean();
		Head head = new Head();
		//数据容器
		List<TmDictDataVo> dictDatas = new ArrayList<TmDictDataVo>();
		try {
			dictDatas.addAll(tmDictDataService
					.findVoByType(PhoneConstantUtil.termianlBTModel));
			if(CollectionUtil.listNotEmptyNotSizeZero(dictDatas)){
				bean.setBusinessObject(dictDatas);
			}
			head.setCode(Globals.RETURN_SUCCESS);
			head.setMessage("获取主销型号信息成功");
		} catch (Exception e) {
			head.setCode(Globals.RETURN_FAIL);
			head.setMessage("获取主销型号信息失败,"+e.getMessage());
			e.printStackTrace();
		}
		bean.setHead(head);
		return bean;
	}

	/**
	 * 终端信息-我的申请.
	 * <AUTHOR>
	 * @param tmTerminalExtendVo
	 * 		终端vo
	 * @return
	 * 		终端信息
	 */
	@RequestMapping(params="findMyApplyTerm")
	@ResponseBody
	public ResponseBean findMyApplyTerm(TmTerminalExtendVo tmTerminalExtendVo){
		ResponseBean bean = new ResponseBean();
		Head head = new Head();
		Page page = new Page();
		page.setPage(tmTerminalExtendVo.getPage());
		try {
			List<TmTerminalExtendVo> tmTerminalExtendVos = tmTerminalForSciService.findMyApplyTerm(tmTerminalExtendVo, page);
			head.setCode(Globals.RETURN_SUCCESS);
			head.setMessage("获取门店申请信息成功");
			bean.setBusinessObject(tmTerminalExtendVos);
		} catch (Exception e) {
			head.setCode(Globals.RETURN_FAIL);
			head.setMessage("获取门店申请信息失败,"+e.getMessage());
			e.printStackTrace();
		}
		bean.setHead(head);
		return bean;
	}
	/**
	 * 终端信息-我的审批.
	 * <AUTHOR>
	 * @param tmTerminalExtendVo
	 * 		终端vo
	 * @return
	 * 		终端信息
	 */
	@RequestMapping(params="findMyApprovalTerm")
	@ResponseBody
	public ResponseBean findMyApprovalTerm(TmTerminalExtendVo tmTerminalExtendVo){
		ResponseBean bean = new ResponseBean();
		Head head = new Head();
		Page page = new Page();
		page.setPage(tmTerminalExtendVo.getPage());
		try {
			List<TmTerminalExtendVo> tmTerminalExtendVos = tmTerminalForSciService
					.findMyApprovalTerm(tmTerminalExtendVo, page);
			for (int i = 0; i < tmTerminalExtendVos.size(); i++) {
				if(StringUtil.isNotBlank(tmTerminalExtendVos.get(i).getChannelType())) {
					tmTerminalExtendVos.get(i).setChannelType(DictUtil.getDictDataValueByCode(PhoneConstantUtil.channel, tmTerminalExtendVos.get(i).getChannelType()));
				}
			}
			head.setCode(Globals.RETURN_SUCCESS);
			head.setMessage("获取门店审批信息成功");
			bean.setBusinessObject(tmTerminalExtendVos);
		} catch (Exception e) {
			head.setCode(Globals.RETURN_FAIL);
			head.setMessage("获取门店审批信息失败,"+e.getMessage());
			e.printStackTrace();
		}
		bean.setHead(head);
		return bean;
	}/**
	 * 终端信息-我的审批.
	 * <AUTHOR>
	 * @param tmTerminalExtendVo
	 * 		终端vo
	 * @return
	 * 		终端信息
	 */
	@RequestMapping(params="findMyApprovalTermNum")
	@ResponseBody
	public ResponseBean findMyApprovalTermNum(TmTerminalExtendVo tmTerminalExtendVo){
		ResponseBean bean = new ResponseBean();
		Head head = new Head();
		try {
			Integer  num = tmTerminalForSciService.findMyApprovalTermNum(tmTerminalExtendVo);
			head.setCode(Globals.RETURN_SUCCESS);
			head.setMessage("获取门店审批信息数量成功");
			bean.setBusinessObject(num!=null?num:0);
		} catch (BusinessException e) {
			head.setCode(Globals.RETURN_FAIL);
			head.setMessage("获取门店审批信息数量失败,"+e.getMessage());
		} catch (Exception e) {
			head.setCode(Globals.RETURN_FAIL);
			head.setMessage("获取门店审批信息数量失败,"+e.getMessage());
			e.printStackTrace();
		}
		bean.setHead(head);
		return bean;
	}

	/**
	 * 终端信息-列表.---针对经销商用户---其他用户不生效
	 * @param tmTerminalExtendVo
	 * @return
	 * @url : tmTerminalForSfaController.do?findTerminalListMain
	 */
	@RequestMapping(params = "findTerminalListMainXNY")
	@ResponseBody
	public ResponseBean findTerminalListMainXNY(TmTerminalExtendVo tmTerminalExtendVo){
		ResponseBean json = new ResponseBean();
		Head head = new Head();
		//设置分页
		Page page= setPage(tmTerminalExtendVo.getPage(),tmTerminalExtendVo.getRows());
		head.setCode(Globals.RETURN_FAIL);
		String title = "获取列表";
		head.setMessage( title + "失败");
		try {
			List<TmTerminalExtendVo> tmTerminalExtendVos = tmTerminalForSciService.findTerminalListMain(tmTerminalExtendVo,page);
			json.setBusinessObject(tmTerminalExtendVos);
			head.setCode(Globals.RETURN_SUCCESS);
			head.setMessage(title + "成功");
		}catch (Exception e){
			e.printStackTrace();
			if (StringUtil.isNotEmpty(e.getMessage())) {
				head.setMessage(head.getMessage() + "," + e.getMessage());
			}
		}
		json.setHead(head);
		return json;
	}

//	/**
//	 * 终端信息-列表.---针对经销商用户---其他用户不生效
//	 * @param tmTerminalExtendVo
//	 * @return
//	 * @url : tmTerminalForSfaController.do?findTerminalListMain
//	 */
//	@RequestMapping(params = "findTerminalListMain", method = RequestMethod.POST)
//	@ResponseBody
//	public ResponseBean findTerminalListMain(TmTerminalExtendVo tmTerminalExtendVo){
//		log.info("=== 进入 findTerminalListMain 方法 ===");
//		ResponseBean json = new ResponseBean();
//		Head head = new Head();
//		//设置分页
//		Page page= setPage(tmTerminalExtendVo.getPage(),tmTerminalExtendVo.getRows());
//		head.setCode(Globals.RETURN_FAIL);
//		String title = "获取列表";
//		head.setMessage( title + "失败");
//		try {
//			List<TmTerminalExtendVo> tmTerminalExtendVos = tmTerminalForSciService.findTerminalListMain(tmTerminalExtendVo,page);
//			json.setBusinessObject(tmTerminalExtendVos);
//			head.setCode(Globals.RETURN_SUCCESS);
//			head.setMessage(title + "成功");
//		}catch (Exception e){
//			e.printStackTrace();
//			if (StringUtil.isNotEmpty(e.getMessage())) {
//				head.setMessage(head.getMessage() + "," + e.getMessage());
//			}
//		}
//		json.setHead(head);
//		return json;
//	}

	/**
	 * 查询泰博出行终端
	 *
	 * @return
	 * @url : tmTerminalForSfaController.do?findTbcxTerminal
	 */
	@RequestMapping(params = "findTbcxTerminal", method = RequestMethod.POST)
	@ResponseBody
	public ResponseBean findTbcxTerminal(TmTerminalExtendVo tmTerminalExtendVo) {
		log.info("=== 进入 findTbcxTerminal 方法 ===");
		ResponseBean json = new ResponseBean();
		Head head = new Head();
		if (StringUtil.isBlank(tmTerminalExtendVo.getTerminalTypeName())){
			head.setCode(Globals.RETURN_FAIL);
			head.setMessage( "请先输入手机号或者门店名称进行精确查询");
		}
		//设置分页
		Page page= setPage(tmTerminalExtendVo.getPage(),tmTerminalExtendVo.getRows());
		head.setCode(Globals.RETURN_FAIL);
		String title = "获取列表";
		head.setMessage( title + "失败");
		try {
			List<TeboShopResp> teboShopResps = tmTerminalForSciService.findTbcxTerminalListMain(tmTerminalExtendVo,page);
			json.setBusinessObject(teboShopResps);
			head.setCode(Globals.RETURN_SUCCESS);
			head.setMessage(title + "成功");
		}catch (Exception e){
			e.printStackTrace();
			if (StringUtil.isNotEmpty(e.getMessage())) {
				head.setMessage(head.getMessage() + "," + e.getMessage());
			}
		}
		json.setHead(head);
		return json;
	}

	/**
	 * 查询泰博出行终端
	 *
	 * @return
	 * @url : tsActApplyExecuteController.do?findTbcxTerminal
	 */
	@RequestMapping(params = "findTbcxTerminalByReq")
	@ResponseBody
	public ResponseBean findTbcxTerminalByReq(@RequestParam("terminalCode") String terminalCode) {
		ResponseBean json = new ResponseBean();
		Head head = new Head();
		head.setCode(Globals.RETURN_FAIL);
		String title = "获取数据";
		head.setMessage(title + "失败");
		boolean isVip = false;
		try {
			isVip = tmTerminalForSciService.getVipByTerminalCode(terminalCode);
			head.setCode(Globals.RETURN_SUCCESS);
			head.setMessage(title + "成功");

		} catch (Exception e) {
			e.printStackTrace();
			if (StringUtil.isNotEmpty(e.getMessage())) {
				head.setMessage(title + "失败:" + e.getMessage());
			}
		}
		json.setBusinessObject(isVip);
		json.setHead(head);
		return json;
	}


	private Page setPage(String pageStr,String rowsStr){
		Page page = new Page();
		if(!StringUtil.isNotEmpty(pageStr)){
			pageStr = "1";
		}
		if(!StringUtil.isNotEmpty(rowsStr)){
			rowsStr = "5";
		}
		page.setPage(pageStr);
		page.setRows(rowsStr);
		return page;
	}
	/**
	 * 终端信息-详情.
	 * <AUTHOR>
	 * @param tmTerminalExtendVo
	 * 		终端vo
	 * @return
	 * 		终端信息
	 */
	@RequestMapping(params="findTerminalDetail")
	@ResponseBody
	public ResponseBean findTerminalDetail(TmTerminalExtendVo tmTerminalExtendVo){
		ResponseBean bean = new ResponseBean();
		Head head = new Head();
		Page page = new Page();
		page.setPage(tmTerminalExtendVo.getPage());
		try {
			tmTerminalExtendVo = tmTerminalForSciService.findTerminalDetail(tmTerminalExtendVo);
			head.setCode(Globals.RETURN_SUCCESS);
			head.setMessage("获取门店信息成功");
			bean.setBusinessObject(tmTerminalExtendVo);
		} catch (Exception e) {
			head.setCode(Globals.RETURN_FAIL);
			head.setMessage("获取门店信息失败,"+e.getMessage());
			e.printStackTrace();
		}
		bean.setHead(head);
		return bean;
	}
	/**
	 * 终端审批执行.
	 * <AUTHOR>
	 * @param tmTerminalForSciWorkFlowVo
	 * 		审批vo
	 * @return
	 * 		审批结果
	 */
	@RequestMapping(params="executeApprovalTerm")
	@ResponseBody
	public ResponseBean executeApprovalTerm(TmTerminalForSciWorkFlowVo tmTerminalForSciWorkFlowVo){
		ResponseBean bean = new ResponseBean();
		Head head = new Head();
		try {
			tmTerminalForSciService.executeApprovalTerm(tmTerminalForSciWorkFlowVo);
			head.setCode(Globals.RETURN_SUCCESS);
			head.setMessage("审批成功");
		}catch (BusinessException e){
			head.setCode(Globals.RETURN_FAIL);
			head.setMessage("审批失败,"+e.getMessage());
			e.printStackTrace();
		} catch (Exception e) {
			head.setCode(Globals.RETURN_FAIL);
			head.setMessage("审批失败,"+e.getMessage());
			e.printStackTrace();
		}
		bean.setHead(head);
		return bean;
	}
	/**
	 * 获取终端类型
	 * 		 终端vo扩展对象
	 */
	@RequestMapping(params="findterminalTtype")
	@ResponseBody
	public ResponseBean findterminalTtype(TmTerminalExtendVo tmTerminalExtendVo){
		ResponseBean bean = new ResponseBean();
		Head head = new Head();
		//数据容器
		List<TmDictDataForSci> dictDatas = new ArrayList<TmDictDataForSci>();
		List<TmDictDataForSci> sortDictList = new ArrayList<TmDictDataForSci>();
		try {
			dictDatas = tmTerminalForSciService.findDictTree(PhoneConstantUtil.channel);
			List<String> strCollection = new ArrayList<String>();
			for(TmDictDataForSci vo : dictDatas){
				strCollection.add(vo.getDictCode());
			}
			Collections.sort(strCollection);
			
			for(String strNum : strCollection){
				for (TmDictDataForSci vo : dictDatas) {
					if(strNum.equals(vo.getDictCode())){
						sortDictList.add(vo);
						continue;
					}
				}
			}
			
			if(CollectionUtil.listNotEmptyNotSizeZero(sortDictList)){
				bean.setBusinessObject(sortDictList);
			}
			head.setCode(Globals.RETURN_SUCCESS);
			head.setMessage("获取位置类型信息成功");
		} catch (Exception e) {
			head.setCode(Globals.RETURN_FAIL);
			head.setMessage("获取位置类型信息失败,"+e.getMessage());
			e.printStackTrace();
		}
		bean.setHead(head);
		return bean;
	}
	/**
	 * 获取所属经销商
	 * 		 终端vo扩展对象
	 * @url : tmTerminalForSfaController.do?findCustomerByPosId
	 */
	@RequestMapping(params="findCustomerByPosId")
	@ResponseBody
	public ResponseBean findCustomerByPosId(TmCustomerSfaVo qtcVo){
		ResponseBean bean = new ResponseBean();
		Page page = setPage(qtcVo.getPage(),qtcVo.getRows());
		Head head = new Head();
		try {
			List<TmCustomerVo> tcvoList = tmTerminalForSciService.findTmCustTomerByAllPossible(qtcVo,page);
			head.setCode(Globals.RETURN_SUCCESS);
			head.setMessage("获取所属经销商成功");
			bean.setBusinessObject(tcvoList);
		} catch (Exception e) {
			head.setCode(Globals.RETURN_FAIL);
			head.setMessage("获取所属经销商失败,"+e.getMessage());
			e.printStackTrace();
		}
		bean.setHead(head);
		return bean;
	}


	/**
	 * 编辑终端信息
	 * 		 终端vo扩展对象
	 */
	@RequestMapping(params="editTmTerminal")
	@ResponseBody
	public ResponseBean editTmTerminal(TmTerminalExtendVo tmTerminalExtendVo){
		ResponseBean bean = new ResponseBean();
		Head head = new Head();
		try {
			TmTerminalEntity terminal = tmCustomerService.get(TmTerminalEntity.class, tmTerminalExtendVo.getId());
			if(("0").equals(terminal.getExtChar7())){
				head.setCode(Globals.RETURN_FAIL);
				head.setMessage("该门店未通过审核，不可编辑");
				bean.setHead(head);
				return bean;
			}
			tmTerminalForSciService.editTmTerminal(tmTerminalExtendVo,terminal);
			head.setCode(PhoneConstantUtil.RETURN_SAVE);
			head.setMessage("编辑门店信息成功");
		} catch (Exception e) {
			head.setCode(Globals.RETURN_FAIL);
			head.setMessage("编辑门店信息失败,"+e.getMessage());
			e.printStackTrace();
		}
		bean.setHead(head);
		return bean;
	}

	/**
	 * 验证门店重复性
	 */
	@RequestMapping(params="checkTmTerminal")
	@ResponseBody
	public ResponseBean checkTmTerminal(TmTerminalExtendVo tmTerminalExtendVo){
		ResponseBean bean = new ResponseBean();
		Head head = new Head();
		try {
			CheckTmTerminalExist b = tmTerminalForSciService.checkTmTerminal(tmTerminalExtendVo);
			head.setCode(Globals.RETURN_SUCCESS);
			bean.setBusinessObject(b);
			head.setMessage("检验门店信息息成功");
		} catch (Exception e) {
			head.setCode(Globals.RETURN_FAIL);
			head.setMessage("检验门店信息失败,"+e.getMessage());
			e.printStackTrace();
		}
		bean.setHead(head);
		return bean;
	}

}
